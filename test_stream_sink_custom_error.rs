// Test file to verify StreamSink custom error support

use crate::frb_generated::StreamSink;
use anyhow::Result;
use backtrace::Backtrace;

pub enum CustomError {
    ErrorOne { message: String, backtrace: Backtrace },
    ErrorTwo { code: u32, backtrace: Backtrace },
}

// Test function with custom error type as second parameter
pub fn test_stream_sink_custom_error(
    sink: StreamSink<String, CustomError, flutter_rust_bridge::SseCodec>,
) -> Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(CustomError::ErrorOne {
        message: "Custom error from StreamSink".to_string(),
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

// Test function with custom error type as second parameter (no explicit codec)
pub fn test_stream_sink_custom_error_no_codec(
    sink: StreamSink<String, CustomError>,
) -> Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(CustomError::ErrorTwo {
        code: 42,
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

// Test function with traditional codec as second parameter (should still work)
pub fn test_stream_sink_traditional(
    sink: StreamSink<String, flutter_rust_bridge::SseCodec>,
) -> Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(anyhow::anyhow!("Traditional anyhow error")).unwrap();
    Ok(())
}

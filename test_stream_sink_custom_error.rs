// Test file to verify StreamSink custom error support

use crate::frb_generated::StreamSink;
use anyhow::Result;
use backtrace::Backtrace;

pub enum CustomError {
    ErrorOne { message: String, backtrace: Backtrace },
    ErrorTwo { code: u32, backtrace: Backtrace },
}

// Test function - should work with existing StreamSink if custom errors implement the right traits
pub fn test_stream_sink_custom_error_simple(
    sink: StreamSink<String>,
) -> Result<()> {
    sink.add("Hello".to_string()).unwrap();

    // This should work if CustomError implements the required traits
    sink.add_error(CustomError::ErrorOne {
        message: "Custom error from StreamSink".to_string(),
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

// Test function with explicit codec
pub fn test_stream_sink_custom_error_with_codec(
    sink: StreamSink<String, flutter_rust_bridge::SseCodec>,
) -> Result<()> {
    sink.add("Hello".to_string()).unwrap();

    // This should work if CustomError implements SseEncode
    sink.add_error(CustomError::ErrorTwo {
        code: 42,
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

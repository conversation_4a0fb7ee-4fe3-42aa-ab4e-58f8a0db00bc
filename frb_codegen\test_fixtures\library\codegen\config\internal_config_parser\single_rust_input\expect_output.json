{"controller": {"exclude_paths": ["{the-working-directory}/src/frb_generated.rs"], "max_count": null, "watch": false, "watching_paths": ["{the-working-directory}/src"]}, "dumper": {"dump_contents": [], "dump_directory": "{the-working-directory}/target/frb_dump"}, "generator": {"api_dart": {"dart3": true, "dart_decl_base_output_path": "{the-working-directory}/my_dart_folder", "dart_entrypoint_class_name": "RustLib", "dart_enums_style": true, "dart_impl_output_path": {"common": "{the-working-directory}/my_dart_folder/frb_generated.dart", "io": "{the-working-directory}/my_dart_folder/frb_generated.io.dart", "web": "{the-working-directory}/my_dart_folder/frb_generated.web.dart"}, "dart_preamble": "", "dart_type_rename": {}}, "wire": {"c": {"c_output_path": "{the-working-directory}/frb_generated.h", "c_symbol_prefix": "frbgen_fake_dart_package_", "enable": false, "rust_crate_dir": "{the-working-directory}", "rust_output_path": "{the-working-directory}/src/frb_generated.rs"}, "dart": {"c_symbol_prefix": "frbgen_fake_dart_package_", "dart_impl_output_path": {"common": "{the-working-directory}/my_dart_folder/frb_generated.dart", "io": "{the-working-directory}/my_dart_folder/frb_generated.io.dart", "web": "{the-working-directory}/my_dart_folder/frb_generated.web.dart"}, "dart_output_class_name_pack": {"api_class_name": "RustLibApi", "api_impl_class_name": "RustLibApiImpl", "api_impl_platform_class_name": "RustLibApiImplPlatform", "entrypoint_class_name": "RustLib", "wasm_module_name": "RustLibWasmModule", "wire_class_name": "RustLibWire"}, "dart_root": "{the-working-directory}", "default_external_library_loader": {"io_directory": "target/release/", "stem": "UNKNOWN", "web_prefix": "pkg/"}, "extra_headers": "", "has_ffigen": false, "llvm_compiler_opts": "", "llvm_path": ["/opt/homebrew/opt/llvm", "/usr/local/opt/llvm", "/usr/lib/llvm-9", "/usr/lib/llvm-10", "/usr/lib/llvm-11", "/usr/lib/llvm-12", "/usr/lib/llvm-13", "/usr/lib/llvm-14", "/usr/lib/", "/usr/lib64/", "C:/Program Files/llvm", "C:/msys64/mingw64"], "web_enabled": true}, "rust": {"c_symbol_prefix": "frbgen_fake_dart_package_", "default_rust_opaque_codec": "<PERSON><PERSON>", "default_stream_sink_codec": "Sse", "has_ffigen": false, "rust_crate_dir": "{the-working-directory}", "rust_output_path": "{the-working-directory}/src/frb_generated.rs", "rust_preamble": "", "web_enabled": true}}}, "parser": {"hir": {"parse_const": false, "rust_crate_dir": "{the-working-directory}", "rust_features": null, "rust_input_namespace_pack": {"rust_input_namespace_prefixes": ["crate::api"], "rust_output_path_namespace": "crate::frb_generated"}, "third_party_crate_names": []}, "mir": {"default_dart_async": true, "default_rust_opaque_codec": "<PERSON><PERSON>", "default_stream_sink_codec": "Sse", "enable_lifetime": false, "force_codec_mode_pack": {"dart2rust": "Pde", "rust2dart": "Pde"}, "rust_input_namespace_pack": {"rust_input_namespace_prefixes": ["crate::api"], "rust_output_path_namespace": "crate::frb_generated"}, "stop_on_error": false, "type_64bit_int": false}}, "polisher": {"add_mod_to_lib": true, "build_runner": true, "c_output_path": "{the-working-directory}/frb_generated.h", "dart_fix": true, "dart_format": true, "dart_format_line_length": 80, "dart_output": "{the-working-directory}/my_dart_folder", "dart_root": "{the-working-directory}", "duplicated_c_output_path": [], "enable_auto_upgrade": true, "rust_crate_dir": "{the-working-directory}", "rust_format": true, "rust_output_path": "{the-working-directory}/src/frb_generated.rs", "web_enabled": true}, "preparer": {"dart_root": "{the-working-directory}", "deps_check": true, "needs_ffigen": false}}
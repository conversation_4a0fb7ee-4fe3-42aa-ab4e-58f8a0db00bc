{"constants": [], "enums": [], "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "functions": [{"item_fn": "GeneralizedItemFn(name=func_1, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_anyhow_result, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_result, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_std_result_result, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}], "skips": [], "structs": [], "trait_impls": [], "traits": [], "types": []}
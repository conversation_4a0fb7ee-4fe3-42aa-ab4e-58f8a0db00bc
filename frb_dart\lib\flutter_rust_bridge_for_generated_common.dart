/// {@template flutter_rust_bridge.only_for_generated_code}
/// This is only intended to be used by automatically generated code,
/// instead of developers.
/// {@endtemplate}
library;

export 'dart:async' show FutureOr;
export 'dart:typed_data' hide Int64List, Uint64List;

export 'package:meta/meta.dart' show internal, protected, sealed;

export 'flutter_rust_bridge.dart';
export 'src/codec/cst.dart';
export 'src/codec/dco.dart';
export 'src/codec/pde.dart';
export 'src/codec/sse.dart';
export 'src/dart_opaque/_common.dart';
export 'src/exceptions.dart';
export 'src/loader/_common.dart';
export 'src/main_components/api.dart';
export 'src/main_components/api_impl.dart';
export 'src/main_components/entrypoint.dart';
export 'src/main_components/handler.dart';
export 'src/main_components/wire.dart';
export 'src/manual_impl/_common.dart';
export 'src/misc/box.dart';
export 'src/misc/rust_opaque.dart';
export 'src/misc/simple_disposable.dart';
export 'src/rust_arc/_common.dart';
export 'src/stream/stream_sink.dart';
export 'src/task.dart';

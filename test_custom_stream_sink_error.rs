// Test file to verify StreamSink custom error support

use flutter_rust_bridge::frb;
use std::backtrace::Backtrace;

#[derive(Debug, Clone)]
pub enum MyCustomError {
    ErrorOne { message: String, backtrace: Backtrace },
    ErrorTwo { code: u32, backtrace: Backtrace },
}

// Test function using StreamSink with custom error type as second parameter
#[frb]
pub fn test_stream_sink_custom_error(
    sink: flutter_rust_bridge::StreamSink<String, MyCustomError>,
) -> anyhow::Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(MyCustomError::ErrorOne {
        message: "Custom error from StreamSink".to_string(),
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

// Test function using StreamSink with custom error type and explicit codec
#[frb]
pub fn test_stream_sink_custom_error_with_codec(
    sink: flutter_rust_bridge::StreamSink<String, MyCustomError, flutter_rust_bridge::SseCodec>,
) -> anyhow::Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(MyCustomError::ErrorTwo {
        code: 42,
        backtrace: Backtrace::new(),
    }).unwrap();
    Ok(())
}

// Test function using traditional StreamSink (should still work)
#[frb]
pub fn test_stream_sink_traditional(
    sink: flutter_rust_bridge::StreamSink<String>,
) -> anyhow::Result<()> {
    sink.add("Hello".to_string()).unwrap();
    sink.add_error(anyhow::anyhow!("Traditional anyhow error")).unwrap();
    Ok(())
}

{"constants": [], "enums": [], "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "functions": [{"item_fn": "GeneralizedItemFn(name=func_one, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api_one", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_two, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api_two", "owner": "Function", "sources": ["Normal"]}], "skips": [], "structs": [], "trait_impls": [], "traits": [], "types": []}
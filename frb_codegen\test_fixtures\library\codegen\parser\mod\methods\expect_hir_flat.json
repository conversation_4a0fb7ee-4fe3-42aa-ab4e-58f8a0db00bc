{"constants": [], "enums": [{"mirror": false, "name": "crate::api/MyEnum", "sources": ["Normal"], "visibility": "Public"}], "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "functions": [{"item_fn": "GeneralizedItemFn(name=example_instance_method, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": {"StructOrEnum": {"impl_ty": "MyEnum", "trait_def_name": null}}, "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=example_static_method, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": {"StructOrEnum": {"impl_ty": "MyEnum", "trait_def_name": null}}, "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=example_instance_method, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": {"StructOrEnum": {"impl_ty": "MyStruct", "trait_def_name": null}}, "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=example_static_method, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": {"StructOrEnum": {"impl_ty": "MyStruct", "trait_def_name": null}}, "sources": ["Normal"]}], "skips": [], "structs": [{"mirror": false, "name": "crate::api/MyStruct", "sources": ["Normal"], "visibility": "Public"}], "trait_impls": [], "traits": [], "types": []}
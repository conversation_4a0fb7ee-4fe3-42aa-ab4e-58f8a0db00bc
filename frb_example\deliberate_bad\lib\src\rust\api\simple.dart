// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Future<void> makeStackBufferOverflow() =>
    RustLib.instance.api.crateApiSimpleMakeStackBufferOverflow();

Future<void> makeHeapUseAfterFree() =>
    RustLib.instance.api.crateApiSimpleMakeHeapUseAfterFree();

Future<void> makeUseOfUninitializedValue() =>
    RustLib.instance.api.crateApiSimpleMakeUseOfUninitializedValue();

Future<void> makeMemoryLeak() =>
    RustLib.instance.api.crateApiSimpleMakeMemoryLeak();

Future<void> makeDataRace() =>
    RustLib.instance.api.crateApiSimpleMakeDataRace();

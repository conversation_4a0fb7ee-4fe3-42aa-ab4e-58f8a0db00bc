{"dart_code_of_type": {}, "enum_pool": {}, "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "extra_types_all": [], "funcs_all": [{"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 1, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_one"}, "namespace": "crate::api_one", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 2, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_two"}, "namespace": "crate::api_two", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}], "skips": [], "struct_pool": {}, "trait_impls": []}
{"dart_code_of_type": {}, "enum_pool": {}, "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "extra_types_all": [], "funcs_all": [{"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 1, "impl_mode": "Normal", "initializer": false, "inputs": [{"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "chrono_duration"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": {"Time": "Duration"}, "safe_ident": "Chrono_Duration", "type": "Delegate"}}, "needs_extend_lifetime": false, "ownership_mode": "Owned"}, {"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "uuid_uuid"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": "<PERSON><PERSON>", "safe_ident": "<PERSON><PERSON>", "type": "Delegate"}}, "needs_extend_lifetime": false, "ownership_mode": "Owned"}], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_1"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 2, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_anyhow_result"}, "namespace": "crate::api", "output": {"error": {"data": "String", "safe_ident": "String", "type": "Delegate"}, "normal": {"data": "I32", "safe_ident": "i_32", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 3, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_result"}, "namespace": "crate::api", "output": {"error": {"data": "String", "safe_ident": "String", "type": "Delegate"}, "normal": {"data": "I32", "safe_ident": "i_32", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 4, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_std_result_result"}, "namespace": "crate::api", "output": {"error": {"data": "String", "safe_ident": "String", "type": "Delegate"}, "normal": {"data": "I32", "safe_ident": "i_32", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}], "skips": [], "struct_pool": {}, "trait_impls": []}
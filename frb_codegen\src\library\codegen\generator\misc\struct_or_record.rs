use crate::codegen::generator::codec::sse::lang::<PERSON>;
use crate::codegen::ir::mir::field::<PERSON><PERSON>ield;

pub(crate) enum StructOrRecord {
    Struct,
    Record,
}

impl StructOrRecord {
    pub(crate) fn field_name(
        &self,
        index: usize,
        field: &MirField,
        is_field_named: bool,
        lang: &Lang,
    ) -> String {
        match lang {
            Lang::DartLang(_) => match self {
                StructOrRecord::Struct => field.name.dart_style(),
                StructOrRecord::Record => format!("${}", index + 1),
            },
            Lang::RustLang(_) => match self {
                StructOrRecord::Struct => {
                    if is_field_named {
                        field.name.rust_style(false).to_owned()
                    } else {
                        format!("{}", index)
                    }
                }
                StructOrRecord::Record => format!("{}", index),
            },
        }
    }
}

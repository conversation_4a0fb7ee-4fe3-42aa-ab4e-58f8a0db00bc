{"dart_code_of_type": {}, "enum_pool": {"crate::another_file/EnumInAnotherFile": {"comments": [], "ignore": false, "mode": "Simple", "name": "crate::another_file/EnumInAnotherFile", "needs_json_serializable": false, "variants": [{"comments": [], "kind": "Value", "name": {"dart_style": null, "rust_style": "Apple"}, "wrapper_name": {"dart_style": null, "rust_style": "EnumInAnotherFile_Apple"}}], "wrapper_name": null}}, "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "extra_types_all": [], "funcs_all": [{"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 1, "impl_mode": "Normal", "initializer": false, "inputs": [{"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "s"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": {"exist_in_real_api": false, "inner": {"data": {"ident": "crate::another_file/StructInAnotherFile", "is_exception": false}, "safe_ident": "struct_in_another_file", "type": "StructRef"}}, "safe_ident": "box_autoadd_struct_in_another_file", "type": "Boxed"}}, "needs_extend_lifetime": false, "ownership_mode": "Owned"}, {"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "e"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": {"PrimitiveEnum": {"mir": {"ident": "crate::another_file/EnumInAnotherFile", "is_exception": false}, "repr": "I32"}}, "safe_ident": "enum_in_another_file", "type": "Delegate"}}, "needs_extend_lifetime": false, "ownership_mode": "Owned"}], "mode": "Normal", "name": {"dart_style": null, "rust_style": "func_one"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": "Function", "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}], "skips": [], "struct_pool": {"crate::another_file/StructInAnotherFile": {"comments": [], "dart_metadata_raw": [], "fields": [{"comments": [], "default": null, "is_final": true, "is_rust_public": false, "name": {"dart_style": null, "rust_style": "a"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": "I32", "safe_ident": "i_32", "type": "Primitive"}}], "generate_eq": true, "generate_hash": true, "ignore": false, "is_fields_named": true, "name": "crate::another_file/StructInAnotherFile", "needs_json_serializable": false, "ui_state": false, "wrapper_name": null}}, "trait_impls": []}
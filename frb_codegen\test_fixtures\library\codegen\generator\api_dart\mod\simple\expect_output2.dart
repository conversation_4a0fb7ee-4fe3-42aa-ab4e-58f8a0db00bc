// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ {VERSION}.

// test for dart_preamble

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import 'frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            

            class Simple  {
                final int val;

                const Simple({required this.val ,});

                
                

                
        @override
        int get hashCode => val.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is Simple &&
                runtimeType == other.runtimeType
                && val == other.val;
        
            }
            
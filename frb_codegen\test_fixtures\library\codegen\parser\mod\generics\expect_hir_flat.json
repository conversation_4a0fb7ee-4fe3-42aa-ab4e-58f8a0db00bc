{"constants": [], "enums": [{"mirror": false, "name": "crate::api/MyGenericEnum", "sources": ["Normal"], "visibility": "Public"}], "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "functions": [{"item_fn": "GeneralizedItemFn(name=func_enum_bool, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_enum_string, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_struct_bool, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_struct_string, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}, {"item_fn": "GeneralizedItemFn(name=func_struct_string_repeated, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}], "skips": [], "structs": [{"mirror": false, "name": "crate::api/MyGenericStruct", "sources": ["Normal"], "visibility": "Public"}], "trait_impls": [], "traits": [], "types": []}
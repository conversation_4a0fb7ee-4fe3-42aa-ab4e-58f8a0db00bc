# reference https://github.com/flutter/flutter/blob/4ad8c15f34/.github/workflows/lock.yaml
name: Lock Thread

on:
  schedule:
    - cron: "0 * * * *"

jobs:
  lock:
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/lock-threads@486f7380c15596f92b724e4260e4981c68d6bde6
        with:
          github-token: ${{ github.token }}
          issue-lock-inactive-days: "14"
          issue-lock-comment: >
            This thread has been automatically locked since there has not been
            any recent activity after it was closed. If you are still experiencing a
            similar issue, please open a new issue.

{"constants": [], "enums": [{"mirror": false, "name": "crate::another_file/EnumInAnotherFile", "sources": ["Normal"], "visibility": "Public"}], "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "functions": [{"item_fn": "GeneralizedItemFn(name=func_one, vis=Some(Visibility::Public(Pub)), attrs=[])", "namespace": "crate::api", "owner": "Function", "sources": ["Normal"]}], "skips": [], "structs": [{"mirror": false, "name": "crate::another_file/StructInAnotherFile", "sources": ["Normal"], "visibility": "Public"}], "trait_impls": [], "traits": [], "types": []}
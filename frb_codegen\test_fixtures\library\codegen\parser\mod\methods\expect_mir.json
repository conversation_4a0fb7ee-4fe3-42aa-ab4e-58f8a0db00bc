{"dart_code_of_type": {}, "enum_pool": {"crate::api/MyEnum": {"comments": [], "ignore": false, "mode": "Simple", "name": "crate::api/MyEnum", "needs_json_serializable": false, "variants": [{"comments": [], "kind": "Value", "name": {"dart_style": null, "rust_style": "A"}, "wrapper_name": {"dart_style": null, "rust_style": "MyEnum_A"}}, {"comments": [], "kind": "Value", "name": {"dart_style": null, "rust_style": "B"}, "wrapper_name": {"dart_style": null, "rust_style": "MyEnum_B"}}], "wrapper_name": null}}, "existing_handler": null, "extra_dart_output_code": {"body": "", "header": {"file_top": "", "import": "", "part": ""}}, "extra_rust_output_code": "", "extra_types_all": [], "funcs_all": [{"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 1, "impl_mode": "Normal", "initializer": false, "inputs": [{"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "that"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": {"PrimitiveEnum": {"mir": {"ident": "crate::api/MyEnum", "is_exception": false}, "repr": "I32"}}, "safe_ident": "my_enum", "type": "Delegate"}}, "needs_extend_lifetime": false, "ownership_mode": "Ref"}], "mode": "Normal", "name": {"dart_style": null, "rust_style": "my_enum_example_instance_method"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": {"Method": {"actual_method_dart_name": null, "actual_method_name": "example_instance_method", "mode": "Instance", "owner_ty": {"data": {"PrimitiveEnum": {"mir": {"ident": "crate::api/MyEnum", "is_exception": false}, "repr": "I32"}}, "safe_ident": "my_enum", "type": "Delegate"}, "owner_ty_raw": "MyEnum", "trait_def": null}}, "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 2, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "my_enum_example_static_method"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": {"Method": {"actual_method_dart_name": null, "actual_method_name": "example_static_method", "mode": "Static", "owner_ty": {"data": {"PrimitiveEnum": {"mir": {"ident": "crate::api/MyEnum", "is_exception": false}, "repr": "I32"}}, "safe_ident": "my_enum", "type": "Delegate"}, "owner_ty_raw": "MyEnum", "trait_def": null}}, "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 3, "impl_mode": "Normal", "initializer": false, "inputs": [{"inner": {"comments": [], "default": null, "is_final": true, "is_rust_public": null, "name": {"dart_style": null, "rust_style": "that"}, "settings": {"is_in_mirrored_enum": false, "skip_auto_accessors": false}, "ty": {"data": {"exist_in_real_api": false, "inner": {"data": {"ident": "crate::api/MyStruct", "is_exception": false}, "safe_ident": "my_struct", "type": "StructRef"}}, "safe_ident": "box_autoadd_my_struct", "type": "Boxed"}}, "needs_extend_lifetime": false, "ownership_mode": "Ref"}], "mode": "Normal", "name": {"dart_style": null, "rust_style": "my_struct_example_instance_method"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": {"Method": {"actual_method_dart_name": null, "actual_method_name": "example_instance_method", "mode": "Instance", "owner_ty": {"data": {"ident": "crate::api/MyStruct", "is_exception": false}, "safe_ident": "my_struct", "type": "StructRef"}, "owner_ty_raw": "MyStruct", "trait_def": null}}, "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}, {"accessor": null, "arg_mode": "Named", "codec_mode_pack": {"dart2rust": "Cst", "rust2dart": "Dco"}, "comments": [], "id": 4, "impl_mode": "Normal", "initializer": false, "inputs": [], "mode": "Normal", "name": {"dart_style": null, "rust_style": "my_struct_example_static_method"}, "namespace": "crate::api", "output": {"error": null, "normal": {"data": "Unit", "safe_ident": "unit", "type": "Primitive"}}, "owner": {"Method": {"actual_method_dart_name": null, "actual_method_name": "example_static_method", "mode": "Static", "owner_ty": {"data": {"ident": "crate::api/MyStruct", "is_exception": false}, "safe_ident": "my_struct", "type": "StructRef"}, "owner_ty_raw": "MyStruct", "trait_def": null}}, "rust_aop_after": null, "rust_async": false, "rust_call_code": null, "stream_dart_await": false}], "skips": [], "struct_pool": {"crate::api/MyStruct": {"comments": [], "dart_metadata_raw": [], "fields": [], "generate_eq": true, "generate_hash": true, "ignore": false, "is_fields_named": true, "name": "crate::api/MyStruct", "needs_json_serializable": false, "ui_state": false, "wrapper_name": null}}, "trait_impls": []}
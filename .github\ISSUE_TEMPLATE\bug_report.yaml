name: Bug Report
description: Create a report to help us improve
labels: ["bug"]
assignees: []
body:
  - type: textarea
    id: describe-the-bug
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to reproduce
      description: A simple way to reproduce is to clone and modify the https://github.com/fzyzcjy/flutter_rust_bridge/tree/master/frb_example/dart_minimal example package according to your needs.
      value: |
        Hint: A simple way to reproduce is to clone and modify the https://github.com/fzyzcjy/flutter_rust_bridge/tree/master/frb_example/dart_minimal example package according to your needs.
        1. ...
        2. ...
        3. ...
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: Run code generator with `RUST_LOG` environment variable set to `debug`. For example, `RUST_LOG=debug flutter_rust_bridge_codegen your_args`.
      placeholder: Run code generator with `RUST_LOG` environment variable set to `debug`. For example, `RUST_LOG=debug flutter_rust_bridge_codegen your_args`.
      render: shell
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        # The following fields are *optional*

        ...but may be useful for debugging

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: false

  - type: textarea
    id: generated-binding
    attributes:
      label: Generated binding code
      description: If possible, please paste your generated binding code, both `.h` (via `--c-output`), and `.dart` (via `--dart-output`), here.
      render: shell
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        Development Environment

  - type: input
    id: env-os
    attributes:
      label: OS
      placeholder: ex. MacOS
    validations:
      required: false
  - type: input
    id: env-version
    attributes:
      label: Version of `flutter_rust_bridge_codegen`
      placeholder: ex. 1.0
    validations:
      required: false
  - type: textarea
    id: env-flutter-doctor
    attributes:
      label: Flutter info
      description: Paste output of `flutter doctor -v`
      render: shell
    validations:
      required: false
  - type: input
    id: env-clang
    attributes:
      label: Version of `clang++`
      placeholder: ex. 12
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Add any other context about the problem here.

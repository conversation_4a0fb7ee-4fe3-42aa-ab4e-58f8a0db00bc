// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ {VERSION}.

// test for dart_preamble

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import 'dep.dart';
import 'frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<void>  firstFunction() => RustLib.instance.api.crateApiFirstFunction();

Future<void>  secondFunction({required Simple arg }) => RustLib.instance.api.crateApiSecondFunction(arg: arg);

            
            
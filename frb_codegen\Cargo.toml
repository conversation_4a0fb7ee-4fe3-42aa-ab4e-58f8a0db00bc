[package]
categories.workspace = true
description.workspace = true
edition.workspace = true
rust-version = "1.74.0"
keywords.workspace = true
license.workspace = true
name = "flutter_rust_bridge_codegen"
repository.workspace = true
version.workspace = true

[lib]
name = "lib_flutter_rust_bridge_codegen"
path = "src/lib.rs"

[[bin]]
name = "flutter_rust_bridge_codegen"
path = "src/main.rs"

[dependencies]
anyhow = { workspace = true }
cargo_metadata = "0.14.1"
convert_case = "0.5.0"
enum_dispatch = "0.3.8"
itertools = "0.10.3"
lazy_static = { workspace = true }
log = "0.4"
fern = { version = "0.6.0", features = ["date-based", "colored"] }
chrono = "0.4"
pathdiff = "0.2.1"
quote = "1.0"
regex = "1.5.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.107"
serde_yaml = "0.9"
strum = "0.26.3"
strum_macros = "0.26.4"
syn = { version = "2.0.26", features = ["full", "extra-traits", "visit-mut"] }
tempfile = "3.2.0"
toml = "0.5.8"
topological-sort = "0.2.2"
enum-iterator = "1.4.0"
clap = { version = "4.4.8", features = ["derive"] }
cbindgen = { version = "0.28.0", default-features = false }
serial_test = "2.0.0"
glob = "0.3.1"
derivative = "2.2.0"
paste = "1.0.14"
proc-macro2 = { version = "1.0.66", features = ["span-locations"] }
include_dir = "0.7.3"
indicatif = "0.17.7"
indicatif-log-bridge = "0.2.2"
notify = "6.1.1"
notify-debouncer-mini = "0.4.1"
cargo_toml = "0.21.0"
hex = "0.4.3"
sha1 = "0.10.6"

[dev-dependencies]
pretty_assertions = "1.4.0"
semver = "1.0.12"

[package.metadata.binstall]
bin-dir = "{bin}{binary-ext}"

[package.metadata.binstall.overrides]
i686-pc-windows-msvc = { pkg-fmt = "zip" }
x86_64-pc-windows-msvc = { pkg-fmt = "zip" }

[features]
default = ["anyhow/backtrace"]

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ['cfg(frb_expand)'] }

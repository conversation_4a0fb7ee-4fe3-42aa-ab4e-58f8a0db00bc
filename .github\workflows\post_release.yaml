# NOTE: Please keep in sync with `ci.yaml`
#
# Ensure doc's steps can work correctly, where users use the released versions
# instead of the code in master branch
name: Post-Release

on:
  workflow_dispatch: { }
  schedule:
    - cron: "0,30 * * * *"

env:
  CARGO_TERM_COLOR: always
  FRB_MAIN_RUST_VERSION: 1.84.0
  FRB_MAIN_DART_VERSION: 3.6.2
  FRB_MAIN_FLUTTER_VERSION: 3.27.4

jobs:
  test_mimic_quickstart:
    name: 'Test :: MimicQuickstart'
    runs-on: ${{ matrix.image }}
    strategy:
      fail-fast: false
      matrix:
        image:
          - windows-2022
          # need macos-"13" because https://github.com/fzyzcjy/flutter_rust_bridge/issues/1225
          - macos-13
          - ubuntu-latest
        codegen_install_mode:
          - cargo-install
          - cargo-binstall
          # - scoop # TODO re-enable after configured
          # - homebrew # TODO re-enable after configured
        exclude:
          - { image: windows-2022, codegen_install_mode: homebrew }
          - { image: macos-13, codegen_install_mode: scoop }
          - { image: ubuntu-latest, codegen_install_mode: homebrew }
          - { image: ubuntu-latest, codegen_install_mode: scoop }

    steps:
      # setup
      - uses: catchpoint/workflow-telemetry-action@v1
        with:
          comment_on_pr: false
      - uses: actions/checkout@v4
        with:
          submodules: recursive
      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.FRB_MAIN_RUST_VERSION }}
          components: rustfmt
      - uses: flutter-actions/setup-flutter@v4
        with:
          cache: true
          version: ${{ env.FRB_MAIN_FLUTTER_VERSION }}
      - if: matrix.codegen_install_mode == 'cargo-binstall'
        uses: cargo-bins/cargo-binstall@main
      - if: runner.os == 'Windows' && matrix.codegen_install_mode == 'scoop'
        uses: MinoruSekine/setup-scoop@main
      # https://docs.flutter.dev/get-started/install/linux#linux-prerequisites
      - if: runner.os == 'Linux'
        run: sudo apt-get update && sudo apt-get install clang cmake git ninja-build pkg-config libgtk-3-dev liblzma-dev libstdc++-12-dev
      - if: runner.os == 'Linux'
        uses: pyvista/setup-headless-display-action@v3

      # execute
      - run: ./frb_internal post-release-mimic-quickstart --codegen-install-mode ${{ matrix.codegen_install_mode }}

import 'package:flutter/material.dart';
import 'package:REPLACE_ME_DART_PACKAGE_NAME/REPLACE_ME_DART_PACKAGE_NAME.dart';

Future<void> main() async {
  await RustLib.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('flutter_rust_bridge quickstart')),
        body: Center(
          child: Text(
              'Action: Call Rust `greet("Tom")`\nResult: `${greet(name: "<PERSON>")}`'),
        ),
      ),
    );
  }
}

{"version": "2.10.0", "description": "Flutter/Dart <-> Rust binding generator, feature-rich, but seamless and simple", "homepage": "https://fzyzcjy.github.io/flutter_rust_bridge/", "license": "MIT", "architecture": {"64bit": {"url": "https://github.com/fzyzcjy/flutter_rust_bridge/releases/download/v2.10.0/flutter_rust_bridge_codegen-x86_64-pc-windows-msvc-v2.10.0.zip", "hash": "Not Found"}, "32bit": {"url": "https://github.com/fzyzcjy/flutter_rust_bridge/releases/download/v2.10.0/flutter_rust_bridge_codegen-i686-pc-windows-msvc-v2.10.0.zip", "hash": "Not Found"}}, "bin": "flutter_rust_bridge_codegen.exe", "checkver": {"github": "https://github.com/fzyzcjy/flutter_rust_bridge"}, "autoupdate": {"architecture": {"64bit": {"url": "https://github.com/fzyzcjy/flutter_rust_bridge/releases/download/v$version/flutter_rust_bridge_codegen-x86_64-pc-windows-msvc-v$version.zip", "hash": {"url": "$baseurl/flutter_rust_bridge_codegen-x86_64-pc-windows-msvc-v$version.zip.sha256"}}, "32bit": {"url": "https://github.com/fzyzcjy/flutter_rust_bridge/releases/download/v$version/flutter_rust_bridge_codegen-i686-pc-windows-msvc-v$version.zip", "hash": {"url": "$baseurl/flutter_rust_bridge_codegen-i686-pc-windows-msvc-v$version.zip.sha256"}}}}}
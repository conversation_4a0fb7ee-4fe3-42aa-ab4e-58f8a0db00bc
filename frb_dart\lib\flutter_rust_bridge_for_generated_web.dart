/// {@macro flutter_rust_bridge.only_for_generated_code}
library;

export 'dart:js_interop';

export 'flutter_rust_bridge_for_generated_common.dart';
export 'src/dart_opaque/_web.dart';
export 'src/droppable/_web.dart' show CrossPlatformFinalizerArg;
export 'src/generalized_uint8list/_web.dart';
export 'src/manual_impl/_web.dart';
export 'src/platform_types/_web.dart';
export 'src/platform_utils/_web.dart';
export 'src/third_party/flutter_foundation_serialization/_web.dart';
export 'src/wasm_module/_web.dart';
